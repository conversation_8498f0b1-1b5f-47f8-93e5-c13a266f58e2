<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 - 可爱风Neumorphism设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 50%, #f0fff0 100%);
            min-height: 100vh;
            padding: 20px;
            color: #5a5a5a;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-family: 'Poppins', sans-serif;
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            animation: gradientShift 4s ease-in-out infinite;
            text-shadow: 0 2px 10px rgba(255, 154, 158, 0.3);
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            justify-items: center;
        }

        .screen {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #ffffff, #f5f5f5);
            border: 1px solid rgba(255, 182, 193, 0.3);
            border-radius: 30px;
            overflow: hidden;
            position: relative;
            box-shadow: 
                15px 15px 30px rgba(255, 182, 193, 0.2),
                -15px -15px 30px rgba(255, 255, 255, 0.8),
                inset 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .screen-title {
            position: absolute;
            top: -50px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #ff9a9e;
            text-shadow: 0 2px 5px rgba(255, 154, 158, 0.3);
        }

        /* Cute Neumorphism Components */
        .cute-card {
            background: linear-gradient(145deg, #ffffff, #f8f8f8);
            border-radius: 25px;
            box-shadow: 
                8px 8px 16px rgba(255, 182, 193, 0.15),
                -8px -8px 16px rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 182, 193, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cute-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 182, 193, 0.3), transparent);
        }

        .cute-card:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 
                12px 12px 24px rgba(255, 182, 193, 0.2),
                -12px -12px 24px rgba(255, 255, 255, 1),
                0 0 20px rgba(255, 182, 193, 0.1);
            filter: brightness(1.05);
        }

        .cute-button {
            background: linear-gradient(145deg, #ffb6c1, #ffc0cb);
            border: none;
            border-radius: 20px;
            color: #ffffff;
            font-weight: 600;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 
                6px 6px 12px rgba(255, 182, 193, 0.3),
                -6px -6px 12px rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
            font-family: 'Nunito', sans-serif;
        }

        .cute-button::before {
            content: '✨';
            position: absolute;
            top: 50%;
            left: -30px;
            transform: translateY(-50%);
            font-size: 16px;
            transition: left 0.5s ease;
        }

        .cute-button:hover::before {
            left: calc(100% + 10px);
        }

        .cute-button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 
                8px 8px 16px rgba(255, 182, 193, 0.4),
                -8px -8px 16px rgba(255, 255, 255, 1);
        }

        .cute-button:active {
            transform: scale(0.95);
            animation: bounce 0.3s ease;
        }

        @keyframes bounce {
            0%, 100% { transform: scale(0.95); }
            50% { transform: scale(1.05); }
        }

        .cute-input {
            background: linear-gradient(145deg, #f8f8f8, #ffffff);
            border: 2px solid rgba(255, 182, 193, 0.2);
            border-radius: 20px;
            color: #5a5a5a;
            padding: 16px 20px;
            font-size: 16px;
            width: 100%;
            box-shadow: 
                inset 4px 4px 8px rgba(255, 182, 193, 0.1),
                inset -4px -4px 8px rgba(255, 255, 255, 0.9);
            font-family: 'Nunito', sans-serif;
        }

        .cute-input:focus {
            outline: none;
            border-color: #ffb6c1;
            box-shadow: 
                inset 4px 4px 8px rgba(255, 182, 193, 0.15),
                inset -4px -4px 8px rgba(255, 255, 255, 1),
                0 0 15px rgba(255, 182, 193, 0.3);
        }

        /* Status Bar */
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #ff9a9e;
            background: rgba(255, 255, 255, 0.8);
        }

        /* Screen Content */
        .screen-content {
            padding: 20px;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }

        .screen-content.full-height {
            height: calc(100% - 44px);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(145deg, #ffffff, #f8f8f8);
            border-top: 1px solid rgba(255, 182, 193, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 -4px 8px rgba(255, 182, 193, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #b0b0b0;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 15px;
        }

        .nav-item.active {
            color: #ff9a9e;
            background: linear-gradient(145deg, #fff0f5, #ffe4e1);
            box-shadow: 
                4px 4px 8px rgba(255, 182, 193, 0.2),
                -4px -4px 8px rgba(255, 255, 255, 0.8);
        }

        .nav-item:hover {
            color: #ff9a9e;
            transform: translateY(-2px) scale(1.1);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
            filter: drop-shadow(0 2px 4px rgba(255, 182, 193, 0.2));
        }

        /* Text Styles */
        .text-primary {
            color: #5a5a5a;
        }

        .text-secondary {
            color: #9a9a9a;
        }

        .text-accent {
            color: #ff9a9e;
        }

        .text-cute {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-large {
            font-size: 28px;
            font-weight: 800;
            font-family: 'Poppins', sans-serif;
        }

        .title-medium {
            font-size: 20px;
            font-weight: 700;
        }

        .body-text {
            font-size: 16px;
            font-weight: 500;
        }

        .caption {
            font-size: 14px;
            font-weight: 400;
        }

        /* Floating Hearts Animation */
        .hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .heart {
            position: absolute;
            font-size: 20px;
            color: #ffb6c1;
            animation: floatHeart 8s infinite linear;
            opacity: 0.7;
        }

        @keyframes floatHeart {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Splash Screen Styles */
        .splash-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
            position: relative;
        }

        .app-logo {
            width: 150px;
            height: 150px;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            box-shadow: 
                20px 20px 40px rgba(255, 182, 193, 0.2),
                -20px -20px 40px rgba(255, 255, 255, 1);
            border: 3px solid rgba(255, 182, 193, 0.2);
            position: relative;
            overflow: hidden;
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .app-logo::before {
            content: '✨💖✨';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 16px;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            fill: #ff9a9e;
            filter: drop-shadow(0 4px 8px rgba(255, 154, 158, 0.3));
        }

        .app-name {
            font-family: 'Poppins', sans-serif;
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }

        .app-tagline {
            font-size: 18px;
            color: #9a9a9a;
            margin-bottom: 50px;
            font-weight: 500;
        }

        /* Dashboard Styles */
        .greeting {
            margin-bottom: 25px;
            text-align: center;
        }

        .greeting-text {
            font-size: 24px;
            font-weight: 700;
            color: #5a5a5a;
            margin-bottom: 8px;
        }

        .greeting-subtext {
            font-size: 16px;
            color: #9a9a9a;
        }

        .daily-intention {
            padding: 25px;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .daily-intention::after {
            content: '🌟';
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        .intention-text {
            font-size: 18px;
            font-style: italic;
            color: #5a5a5a;
            line-height: 1.6;
            font-weight: 500;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .action-button {
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .action-button::before {
            content: '';
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
            border-radius: 50%;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
        }

        .action-button:hover::before {
            opacity: 1;
            transform: scale(1);
        }

        .action-button:hover {
            transform: translateY(-5px) scale(1.05);
        }

        .action-icon {
            width: 32px;
            height: 32px;
            fill: #ff9a9e;
            filter: drop-shadow(0 2px 8px rgba(255, 154, 158, 0.3));
        }

        .action-text {
            font-size: 14px;
            font-weight: 600;
            color: #5a5a5a;
        }

        .goals-summary {
            padding: 25px;
        }

        .summary-title {
            font-size: 18px;
            font-weight: 700;
            color: #5a5a5a;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .summary-title::before {
            content: '🎯';
            margin-right: 8px;
        }

        .goal-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 182, 193, 0.1);
            position: relative;
        }

        .goal-item:last-child {
            border-bottom: none;
        }

        .goal-item::after {
            content: '💫';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            opacity: 0.6;
        }

        .goal-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                6px 6px 12px rgba(255, 182, 193, 0.15),
                -6px -6px 12px rgba(255, 255, 255, 0.9);
        }

        .goal-info {
            flex: 1;
        }

        .goal-title {
            font-size: 16px;
            font-weight: 600;
            color: #5a5a5a;
            margin-bottom: 5px;
        }

        .goal-status {
            font-size: 14px;
            color: #9a9a9a;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 10px;
            overflow: hidden;
            margin-top: 8px;
            box-shadow: inset 2px 2px 4px rgba(255, 182, 193, 0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffb6c1, #ffc0cb, #ffd1dc);
            border-radius: 10px;
            transition: width 1.5s ease;
            box-shadow: 0 0 8px rgba(255, 182, 193, 0.3);
            position: relative;
        }

        .progress-fill::after {
            content: '✨';
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            animation: sparkleMove 2s infinite;
        }

        @keyframes sparkleMove {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Chart Styles */
        .chart-container {
            height: 200px;
            margin: 20px 0;
            position: relative;
            background: linear-gradient(145deg, #ffffff, #f8f8f8);
            border-radius: 20px;
            padding: 20px;
            box-shadow:
                8px 8px 16px rgba(255, 182, 193, 0.1),
                -8px -8px 16px rgba(255, 255, 255, 0.9);
        }

        .chart-svg {
            width: 100%;
            height: 100%;
        }

        .chart-line {
            fill: none;
            stroke: url(#cuteGradient);
            stroke-width: 4;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-in-out forwards;
            filter: drop-shadow(0 2px 4px rgba(255, 182, 193, 0.3));
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .chart-dot {
            fill: #ff9a9e;
            r: 6;
            filter: drop-shadow(0 2px 4px rgba(255, 154, 158, 0.4));
            animation: dotPulse 2s infinite;
        }

        @keyframes dotPulse {
            0%, 100% { r: 6; }
            50% { r: 8; }
        }

        .chart-grid {
            stroke: rgba(255, 182, 193, 0.1);
            stroke-width: 1;
        }

        .chart-label {
            font-size: 12px;
            fill: #9a9a9a;
            text-anchor: middle;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- SVG Icon Definitions -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-home" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </symbol>
            <symbol id="icon-target" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"/>
                <circle cx="12" cy="12" r="6"/>
                <circle cx="12" cy="12" r="2"/>
            </symbol>
            <symbol id="icon-quote" viewBox="0 0 24 24">
                <path d="M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"/>
            </symbol>
            <symbol id="icon-journal" viewBox="0 0 24 24">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3"/>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </symbol>
            <symbol id="icon-add" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="16"/>
                <line x1="8" y1="12" x2="16" y2="12"/>
            </symbol>
            <symbol id="icon-star" viewBox="0 0 24 24">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
            </symbol>
            <symbol id="icon-heart" viewBox="0 0 24 24">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </symbol>
            <symbol id="icon-image" viewBox="0 0 24 24">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <h1 class="title">显化应用 - 可爱风Neumorphism设计 🌸✨</h1>
        
        <div class="screens-grid">
            <!-- Screen 1: Splash Screen -->
            <div class="screen">
                <div class="screen-title">启动页</div>
                <div class="hearts">
                    <div class="heart" style="left: 10%; animation-delay: 0s;">💖</div>
                    <div class="heart" style="left: 20%; animation-delay: 1s;">✨</div>
                    <div class="heart" style="left: 30%; animation-delay: 2s;">🌸</div>
                    <div class="heart" style="left: 40%; animation-delay: 3s;">💫</div>
                    <div class="heart" style="left: 50%; animation-delay: 4s;">🦄</div>
                    <div class="heart" style="left: 60%; animation-delay: 5s;">🌈</div>
                    <div class="heart" style="left: 70%; animation-delay: 0.5s;">💖</div>
                    <div class="heart" style="left: 80%; animation-delay: 1.5s;">✨</div>
                    <div class="heart" style="left: 90%; animation-delay: 2.5s;">🌸</div>
                </div>
                <div class="splash-content">
                    <div class="app-logo">
                        <svg class="logo-icon">
                            <use xlink:href="#icon-star"></use>
                        </svg>
                    </div>
                    <div class="app-name">显化小助手</div>
                    <div class="app-tagline">让梦想变成现实的可爱伙伴 🌟</div>
                    <div class="cute-button">开始奇妙旅程 ✨</div>
                </div>
            </div>

            <!-- Screen 2: Dashboard -->
            <div class="screen">
                <div class="screen-title">主页仪表盘</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div class="greeting">
                        <div class="greeting-text">早安，小可爱 🌸</div>
                        <div class="greeting-subtext">今天也要元气满满地实现梦想哦</div>
                    </div>

                    <div class="cute-card daily-intention">
                        <div class="intention-text">"我是最棒的小仙女，每一天都充满魔法和奇迹，美好的事情正在向我走来 ✨"</div>
                    </div>

                    <div class="quick-actions">
                        <div class="cute-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            <span class="action-text">新建目标 🎯</span>
                        </div>
                        <div class="cute-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-quote"></use>
                            </svg>
                            <span class="action-text">肯定语 💖</span>
                        </div>
                        <div class="cute-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-journal"></use>
                            </svg>
                            <span class="action-text">感恩日记 📝</span>
                        </div>
                        <div class="cute-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-image"></use>
                            </svg>
                            <span class="action-text">愿景板 🌈</span>
                        </div>
                    </div>

                    <div class="cute-card goals-summary">
                        <div class="summary-title">我的小目标</div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 24px; height: 24px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">健康美丽每一天</div>
                                <div class="goal-status">进行中 · 超级棒</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 24px; height: 24px; fill: #87ceeb;">
                                    <use xlink:href="#icon-star"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">事业小成就</div>
                                <div class="goal-status">进行中 · 加油鸭</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 70%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 3: Goals List -->
            <div class="screen">
                <div class="screen-title">目标管理</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h2 class="title-large text-cute">我的小目标 🎯</h2>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            创建
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">🌸</div>
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 15px; box-shadow: 4px 4px 8px rgba(255, 182, 193, 0.2);"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">健康美丽每一天</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">每天运动30分钟，喝足够的水，保持好心情</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 85%;"></div>
                                        </div>
                                        <span class="caption text-accent">85%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">⭐</div>
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 15px; box-shadow: 4px 4px 8px rgba(255, 182, 193, 0.2);"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">事业小成就</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">学习新技能，提升工作能力，获得认可</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 70%;"></div>
                                        </div>
                                        <span class="caption text-accent">70%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">💰</div>
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 15px; box-shadow: 4px 4px 8px rgba(255, 182, 193, 0.2);"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">财富小金库</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">理财投资，建立被动收入，实现财务自由</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 45%;"></div>
                                        </div>
                                        <span class="caption text-accent">45%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 4: Goal Detail with Chart -->
            <div class="screen">
                <div class="screen-title">目标详情</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <image href="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=120&h=120&fit=crop" width="120" height="120" style="border-radius: 25px; margin-bottom: 20px; box-shadow: 8px 8px 16px rgba(255, 182, 193, 0.2);"/>
                        <h2 class="title-large text-cute">健康美丽每一天 🌸</h2>
                        <p class="body-text text-secondary">每天运动30分钟，喝足够的水，保持好心情</p>
                    </div>

                    <div class="cute-card" style="padding: 20px; margin-bottom: 20px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h3 class="title-medium text-accent">开心指数变化 📈</h3>
                        </div>
                        <div class="chart-container">
                            <svg class="chart-svg" viewBox="0 0 300 150">
                                <defs>
                                    <linearGradient id="cuteGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
                                        <stop offset="50%" style="stop-color:#fecfef;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
                                    </linearGradient>
                                    <pattern id="cuteGrid" width="50" height="30" patternUnits="userSpaceOnUse">
                                        <path d="M 50 0 L 0 0 0 30" fill="none" class="chart-grid"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#cuteGrid)" opacity="0.3"/>

                                <path d="M 30 120 L 80 100 L 130 75 L 180 60 L 230 40 L 270 25" class="chart-line"/>

                                <circle cx="30" cy="120" class="chart-dot"/>
                                <circle cx="80" cy="100" class="chart-dot"/>
                                <circle cx="130" cy="75" class="chart-dot"/>
                                <circle cx="180" cy="60" class="chart-dot"/>
                                <circle cx="230" cy="40" class="chart-dot"/>
                                <circle cx="270" cy="25" class="chart-dot"/>

                                <text x="30" y="140" class="chart-label">1月</text>
                                <text x="80" y="140" class="chart-label">2月</text>
                                <text x="130" y="140" class="chart-label">3月</text>
                                <text x="180" y="140" class="chart-label">4月</text>
                                <text x="230" y="140" class="chart-label">5月</text>
                                <text x="270" y="140" class="chart-label">6月</text>
                            </svg>
                        </div>
                        <div style="text-align: center;">
                            <span class="caption text-secondary">开心指数持续上升，当前达到 <span class="text-accent">95%</span> 🎉</span>
                        </div>
                    </div>

                    <div class="cute-card" style="padding: 20px;">
                        <div style="text-align: center; margin-bottom: 15px;">
                            <h3 class="title-medium text-accent">专属肯定语 💖</h3>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div style="background: linear-gradient(145deg, #fff0f5, #ffe4e1); padding: 15px; border-radius: 15px; position: relative;">
                                <div style="position: absolute; top: 10px; right: 15px;">🌟</div>
                                <div style="font-size: 16px; color: #5a5a5a; font-style: italic; line-height: 1.5;">
                                    "我的身体充满活力，每一天都变得更加健康美丽"
                                </div>
                            </div>
                            <div style="background: linear-gradient(145deg, #fff0f5, #ffe4e1); padding: 15px; border-radius: 15px; position: relative;">
                                <div style="position: absolute; top: 10px; right: 15px;">✨</div>
                                <div style="font-size: 16px; color: #5a5a5a; font-style: italic; line-height: 1.5;">
                                    "我爱护自己的身体，给它最好的营养和关爱"
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 5: Affirmations -->
            <div class="screen">
                <div class="screen-title">肯定语花园</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h2 class="title-large text-cute">肯定语花园 🌺</h2>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            种花
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-bottom: 25px; flex-wrap: wrap;">
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(145deg, #ffb6c1, #ffc0cb);">全部 🌸</div>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(145deg, #ffffff, #f8f8f8); color: #ff9a9e;">健康 💪</div>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(145deg, #ffffff, #f8f8f8); color: #ff9a9e;">事业 ⭐</div>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(145deg, #ffffff, #f8f8f8); color: #ff9a9e;">财富 💰</div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px; animation: twinkle 2s infinite;">🌟</div>
                            <div style="font-size: 18px; color: #5a5a5a; line-height: 1.6; margin-bottom: 15px; font-style: italic; font-weight: 500;">
                                "我是最棒的小仙女，每一天都充满魔法和奇迹，美好的事情正在向我走来 ✨"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">通用 · 收藏</span>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">🌸</div>
                            <div style="font-size: 18px; color: #5a5a5a; line-height: 1.6; margin-bottom: 15px; font-style: italic; font-weight: 500;">
                                "我的身体充满活力，每一天都变得更加健康美丽，我爱我自己 💖"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">健康 · 收藏</span>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">⭐</div>
                            <div style="font-size: 18px; color: #5a5a5a; line-height: 1.6; margin-bottom: 15px; font-style: italic; font-weight: 500;">
                                "我的才华被看见和认可，成功自然而然地来到我身边 🌟"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">事业</span>
                                <svg style="width: 20px; height: 20px; fill: #b0b0b0;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">💰</div>
                            <div style="font-size: 18px; color: #5a5a5a; line-height: 1.6; margin-bottom: 15px; font-style: italic; font-weight: 500;">
                                "金钱是能量的流动，我吸引着丰盛的财富进入我的生活 ✨"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">财富</span>
                                <svg style="width: 20px; height: 20px; fill: #87ceeb;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 6: Gratitude Journal -->
            <div class="screen">
                <div class="screen-title">感恩小日记</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h2 class="title-large text-cute">感恩小日记 📝</h2>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            写日记
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">🌸</div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">今天</h3>
                                <span class="caption text-accent">2024.06.18</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff9a9e; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩今天的阳光明媚，让我心情超级好 ☀️</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #87ceeb; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩家人的关爱和温暖的拥抱 🤗</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #98fb98; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩工作中遇到的小惊喜 🎉</span>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">💖</div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">昨天</h3>
                                <span class="caption text-accent">2024.06.17</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff9a9e; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩朋友的陪伴和欢声笑语 😄</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #87ceeb; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩美味的晚餐和温馨的家 🏠</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #98fb98; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩学到的新知识让我成长 📚</span>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 25px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">✨</div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">6月16日</h3>
                                <span class="caption text-accent">2024.06.16</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff9a9e; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩健康的身体让我活力满满 💪</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #87ceeb; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩完成了重要的小目标 🎯</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #98fb98; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩遇到的善意陌生人 😊</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                </div>
            </div>

            <!-- Screen 7: Vision Board -->
            <div class="screen">
                <div class="screen-title">梦想愿景板</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h2 class="title-large text-cute">梦想愿景板 🌈</h2>
                        <div class="cute-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            添加
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 25px;">
                        <div class="cute-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(255, 182, 193, 0.9)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">梦想办公室 ✨</div>
                            </div>
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">🏢</div>
                        </div>
                        <div class="cute-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1; position: relative;">
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">💪</div>
                            <div style="font-size: 18px; font-weight: 700; color: #ff9a9e; margin-bottom: 8px;">健康美丽</div>
                            <div style="font-size: 14px; color: #9a9a9a;">每天都充满活力</div>
                        </div>
                        <div class="cute-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1; position: relative;">
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">⭐</div>
                            <div style="font-size: 18px; font-weight: 700; color: #87ceeb; margin-bottom: 8px;">事业成功</div>
                            <div style="font-size: 14px; color: #9a9a9a;">实现职业梦想</div>
                        </div>
                        <div class="cute-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(255, 182, 193, 0.9)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">财富自由 💰</div>
                            </div>
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">💎</div>
                        </div>
                        <div class="cute-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(255, 182, 193, 0.9)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">旅行世界 🌍</div>
                            </div>
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">✈️</div>
                        </div>
                        <div class="cute-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1; position: relative;">
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 16px;">💕</div>
                            <div style="font-size: 18px; font-weight: 700; color: #98fb98; margin-bottom: 8px;">幸福生活</div>
                            <div style="font-size: 14px; color: #9a9a9a;">被爱包围着</div>
                        </div>
                    </div>

                    <div class="cute-card" style="padding: 25px; text-align: center; position: relative;">
                        <div style="position: absolute; top: 15px; right: 15px; font-size: 20px; animation: twinkle 2s infinite;">🌟</div>
                        <div style="font-size: 20px; font-weight: 700; color: #5a5a5a; margin-bottom: 15px;">我的愿景宣言</div>
                        <div style="font-size: 16px; color: #5a5a5a; line-height: 1.6; font-style: italic; font-weight: 500;">
                            "我是最棒的小仙女，正在创造一个充满健康、成功和丰盛的美好生活。每一天，我都在朝着我的梦想更近一步，宇宙正在为我安排最好的一切 ✨🌈💖"
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-image"></use>
                        </svg>
                        <span>愿景</span>
                    </div>
                </div>
            </div>

            <!-- Screen 8: Settings -->
            <div class="screen">
                <div class="screen-title">设置中心</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="screen-content">
                    <h2 class="title-large text-cute" style="margin-bottom: 25px; text-align: center;">设置中心 ⚙️</h2>

                    <div class="cute-card" style="padding: 25px; margin-bottom: 25px; position: relative;">
                        <div style="position: absolute; top: 15px; right: 15px; font-size: 20px;">👑</div>
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <image href="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=70&h=70&fit=crop" width="70" height="70" style="border-radius: 50%; box-shadow: 4px 4px 8px rgba(255, 182, 193, 0.2);"/>
                            <div style="flex: 1;">
                                <div class="title-medium text-primary" style="margin-bottom: 5px;">小仙女 🧚‍♀️</div>
                                <div class="body-text text-secondary"><EMAIL></div>
                            </div>
                            <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                <use xlink:href="#icon-settings"></use>
                            </svg>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">🔔</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 5px;">每日小提醒</div>
                                    <div class="caption text-secondary">每天9:00温柔提醒写感恩日记</div>
                                </div>
                                <div style="width: 50px; height: 28px; background: linear-gradient(145deg, #ffb6c1, #ffc0cb); border-radius: 14px; position: relative; cursor: pointer; box-shadow: 4px 4px 8px rgba(255, 182, 193, 0.2);">
                                    <div style="width: 24px; height: 24px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease; box-shadow: 2px 2px 4px rgba(255, 182, 193, 0.3);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">💖</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 5px;">肯定语推送</div>
                                    <div class="caption text-secondary">每天3次温暖的肯定语</div>
                                </div>
                                <div style="width: 50px; height: 28px; background: linear-gradient(145deg, #f0f0f0, #ffffff); border-radius: 14px; position: relative; cursor: pointer; box-shadow: inset 2px 2px 4px rgba(255, 182, 193, 0.1);">
                                    <div style="width: 24px; height: 24px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s ease; box-shadow: 2px 2px 4px rgba(255, 182, 193, 0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">🎨</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">主题设置</div>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">☁️</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">云端备份</div>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">💌</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">帮助与反馈</div>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="cute-card" style="padding: 20px; position: relative;">
                            <div style="position: absolute; top: 15px; right: 15px; font-size: 16px;">🌟</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">关于小助手</div>
                                <svg style="width: 20px; height: 20px; fill: #ff9a9e;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add cute interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate floating hearts
            const hearts = document.querySelectorAll('.heart');
            hearts.forEach((heart, index) => {
                heart.style.left = Math.random() * 100 + '%';
                heart.style.animationDelay = Math.random() * 8 + 's';
            });

            // Add bounce effect to buttons
            const buttons = document.querySelectorAll('.cute-button, .action-button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create sparkle effect
                    const sparkle = document.createElement('div');
                    sparkle.innerHTML = '✨';
                    sparkle.style.position = 'absolute';
                    sparkle.style.left = (e.clientX - button.offsetLeft) + 'px';
                    sparkle.style.top = (e.clientY - button.offsetTop) + 'px';
                    sparkle.style.fontSize = '20px';
                    sparkle.style.pointerEvents = 'none';
                    sparkle.style.animation = 'sparkleEffect 1s ease-out forwards';

                    button.style.position = 'relative';
                    button.appendChild(sparkle);

                    setTimeout(() => {
                        sparkle.remove();
                    }, 1000);
                });
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.cute-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add cute loading animation to progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });

        // Add sparkle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkleEffect {
                0% {
                    transform: scale(0) rotate(0deg);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.5) rotate(180deg);
                    opacity: 0.8;
                }
                100% {
                    transform: scale(0) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
