<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人口统计分析报告 - 人体画像数据可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .chart-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .human-figure {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 450px;
            margin: 20px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .figure {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            margin: 0 40px;
        }

        .human-silhouette {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            filter: drop-shadow(0 4px 12px rgba(0,0,0,0.15));
        }

        /* 人体图片样式 */
        .figure-image {
            width: 140px;
            height: 300px;
            object-fit: contain;
            border-radius: 12px;
            background: rgba(255,255,255,0.8);
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .male-figure .figure-image {
            filter: drop-shadow(0 6px 20px rgba(59, 130, 246, 0.25));
            box-shadow:
                0 4px 20px rgba(59, 130, 246, 0.1),
                inset 0 1px 0 rgba(255,255,255,0.6);
        }

        .female-figure .figure-image {
            filter: drop-shadow(0 6px 20px rgba(236, 72, 153, 0.25));
            box-shadow:
                0 4px 20px rgba(236, 72, 153, 0.1),
                inset 0 1px 0 rgba(255,255,255,0.6);
        }

        .figure-image:hover {
            transform: translateY(-5px);
            box-shadow:
                0 8px 30px rgba(0,0,0,0.15),
                inset 0 1px 0 rgba(255,255,255,0.8);
        }

        .figure-label {
            margin-top: 20px;
            font-weight: 700;
            font-size: 18px;
            color: #1e293b;
            text-align: center;
        }

        .figure-stats {
            margin-top: 12px;
            font-size: 13px;
            color: #64748b;
            text-align: center;
            line-height: 1.6;
            background: rgba(255,255,255,0.9);
            padding: 12px 16px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .stat-item:last-child {
            margin-bottom: 0;
        }

        .stat-label {
            font-weight: 500;
            color: #475569;
        }

        .stat-value {
            font-weight: 600;
            color: #1e293b;
        }

        /* 添加悬浮效果和动画 */
        .human-silhouette {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .human-silhouette:hover {
            transform: scale(1.08);
            filter: drop-shadow(0 8px 20px rgba(0,0,0,0.2));
        }

        /* 身高对比线 */
        .height-comparison {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 280px;
            pointer-events: none;
        }

        .height-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 20%, #64748b 50%, transparent 80%);
            opacity: 0.4;
        }

        .height-line.male-line {
            top: 0;
        }

        .height-line.female-line {
            top: 40px;
        }

        .height-label {
            position: absolute;
            right: -60px;
            top: -8px;
            font-size: 11px;
            color: #64748b;
            font-weight: 500;
        }

        /* 背景装饰 */
        .human-figure::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .height-chart {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            height: 300px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .height-bar {
            width: 40px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 5px 5px 0 0;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
        }

        .height-bar .height-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
        }

        .height-value {
            position: absolute;
            top: -25px;
            font-size: 11px;
            color: #667eea;
            font-weight: 600;
        }

        .weight-distribution {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .weight-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .weight-category:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .weight-percentage {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .underweight { color: #17a2b8; }
        .normal { color: #28a745; }
        .overweight { color: #ffc107; }
        .obese { color: #dc3545; }

        .pie-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #4fc3f7 0deg 216deg,
                #f48fb1 216deg 360deg
            );
            margin: 20px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pie-center {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .weight-distribution {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>人口统计分析报告</h1>
            <p>基于2024年全国人口普查数据的综合分析</p>
        </div>

        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number">14.1亿</div>
                <div class="stat-label">总人口</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">51.2%</div>
                <div class="stat-label">男性比例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">48.8%</div>
                <div class="stat-label">女性比例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">169.7cm</div>
                <div class="stat-label">平均身高</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">65.2kg</div>
                <div class="stat-label">平均体重</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">22.6</div>
                <div class="stat-label">平均BMI</div>
            </div>
        </div>

        <div class="main-content">
            <div class="chart-section">
                <h3 class="chart-title">性别比例分布</h3>
                <div class="pie-chart">
                    <div class="pie-center">
                        <div style="font-size: 18px;">性别</div>
                        <div style="font-size: 14px; color: #6c757d;">比例</div>
                    </div>
                </div>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #4fc3f7;"></div>
                        <span>男性 51.2%</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f48fb1;"></div>
                        <span>女性 48.8%</span>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">人体画像对比</h3>
                <div class="human-figure">
                    <!-- 身高对比线 -->
                    <div class="height-comparison">
                        <div class="height-line male-line">
                            <span class="height-label">173.2cm</span>
                        </div>
                        <div class="height-line female-line">
                            <span class="height-label">160.8cm</span>
                        </div>
                    </div>

                    <!-- 男性图形 -->
                    <div class="figure male-figure">
                        <div class="human-silhouette">
                            <img src="images/man.png" alt="男性人体画像" class="figure-image">
                        </div>
                        <div class="figure-label">男性</div>
                        <div class="figure-stats">
                            <div class="stat-item">
                                <span class="stat-label">平均身高:</span>
                                <span class="stat-value">173.2cm</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">平均体重:</span>
                                <span class="stat-value">70.8kg</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">BMI:</span>
                                <span class="stat-value">23.6</span>
                            </div>
                        </div>
                    </div>

                    <!-- 女性图形 -->
                    <div class="figure female-figure">
                        <div class="human-silhouette">
                            <img src="images/woman.png" alt="女性人体画像" class="figure-image">
                        </div>
                        <div class="figure-label">女性</div>
                        <div class="figure-stats">
                            <div class="stat-item">
                                <span class="stat-label">平均身高:</span>
                                <span class="stat-value">160.8cm</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">平均体重:</span>
                                <span class="stat-value">58.9kg</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">BMI:</span>
                                <span class="stat-value">22.8</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">身高分布统计</h3>
                <div class="height-chart">
                    <div class="height-bar" style="height: 60px;">
                        <div class="height-value">8.2%</div>
                        <div class="height-label">150cm以下</div>
                    </div>
                    <div class="height-bar" style="height: 120px;">
                        <div class="height-value">18.5%</div>
                        <div class="height-label">150-160cm</div>
                    </div>
                    <div class="height-bar" style="height: 200px;">
                        <div class="height-value">35.8%</div>
                        <div class="height-label">160-170cm</div>
                    </div>
                    <div class="height-bar" style="height: 180px;">
                        <div class="height-value">28.3%</div>
                        <div class="height-label">170-180cm</div>
                    </div>
                    <div class="height-bar" style="height: 80px;">
                        <div class="height-value">9.2%</div>
                        <div class="height-label">180cm以上</div>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">体重分布分析</h3>
                <div class="weight-distribution">
                    <div class="weight-category">
                        <div class="weight-percentage underweight">12.3%</div>
                        <div>偏瘦</div>
                        <div style="font-size: 12px; color: #6c757d;">BMI < 18.5</div>
                    </div>
                    <div class="weight-category">
                        <div class="weight-percentage normal">58.7%</div>
                        <div>正常</div>
                        <div style="font-size: 12px; color: #6c757d;">18.5 ≤ BMI < 24</div>
                    </div>
                    <div class="weight-category">
                        <div class="weight-percentage overweight">23.1%</div>
                        <div>超重</div>
                        <div style="font-size: 12px; color: #6c757d;">24 ≤ BMI < 28</div>
                    </div>
                </div>
                <div class="weight-distribution">
                    <div class="weight-category">
                        <div class="weight-percentage obese">5.9%</div>
                        <div>肥胖</div>
                        <div style="font-size: 12px; color: #6c757d;">BMI ≥ 28</div>
                    </div>
                    <div class="weight-category" style="grid-column: span 2;">
                        <div style="font-size: 16px; font-weight: 600; color: #28a745;">
                            健康体重人群占比: 58.7%
                        </div>
                        <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                            符合WHO健康标准
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-section full-width">
                <h3 class="chart-title">详细统计数据表</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>统计项目</th>
                            <th>男性</th>
                            <th>女性</th>
                            <th>总体</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>人口数量</td>
                            <td>7.22亿</td>
                            <td>6.88亿</td>
                            <td>14.1亿</td>
                            <td>2024年统计</td>
                        </tr>
                        <tr>
                            <td>平均身高</td>
                            <td>173.2cm</td>
                            <td>160.8cm</td>
                            <td>169.7cm</td>
                            <td>成年人群</td>
                        </tr>
                        <tr>
                            <td>平均体重</td>
                            <td>70.8kg</td>
                            <td>58.9kg</td>
                            <td>65.2kg</td>
                            <td>成年人群</td>
                        </tr>
                        <tr>
                            <td>平均BMI</td>
                            <td>23.6</td>
                            <td>22.8</td>
                            <td>22.6</td>
                            <td>正常范围</td>
                        </tr>
                        <tr>
                            <td>肥胖率</td>
                            <td>7.2%</td>
                            <td>4.6%</td>
                            <td>5.9%</td>
                            <td>BMI≥28</td>
                        </tr>
                        <tr>
                            <td>超重率</td>
                            <td>28.5%</td>
                            <td>17.7%</td>
                            <td>23.1%</td>
                            <td>24≤BMI<28</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>