<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人口统计分析报告 - 人体画像数据可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .chart-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .human-figure {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            height: 500px;
            margin: 20px 0;
            background: linear-gradient(to top, #e3f2fd 0%, #ffffff 100%);
            border-radius: 10px;
            padding: 30px;
            position: relative;
        }

        .figure {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .human-silhouette {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* 头部 */
        .figure-head {
            width: 35px;
            height: 45px;
            border-radius: 50% 50% 45% 45%;
            margin-bottom: 2px;
            position: relative;
            z-index: 3;
        }

        /* 颈部 */
        .figure-neck {
            width: 12px;
            height: 15px;
            margin-bottom: 0;
            border-radius: 6px;
        }

        /* 躯干 */
        .figure-torso {
            width: 45px;
            height: 120px;
            border-radius: 22px 22px 15px 15px;
            margin-bottom: 0;
            position: relative;
        }

        /* 手臂 */
        .figure-arms {
            position: absolute;
            top: 20px;
            width: 70px;
            height: 8px;
            border-radius: 4px;
            left: 50%;
            transform: translateX(-50%);
        }

        .figure-arm-left, .figure-arm-right {
            position: absolute;
            width: 8px;
            height: 60px;
            border-radius: 4px;
            top: 0;
        }

        .figure-arm-left {
            left: -12px;
            transform: rotate(-15deg);
        }

        .figure-arm-right {
            right: -12px;
            transform: rotate(15deg);
        }

        /* 腰部 */
        .figure-waist {
            width: 35px;
            height: 25px;
            border-radius: 17px;
            margin-bottom: 0;
        }

        /* 臀部 */
        .figure-hips {
            width: 40px;
            height: 30px;
            border-radius: 20px 20px 15px 15px;
            margin-bottom: 0;
        }

        /* 腿部 */
        .figure-legs {
            display: flex;
            gap: 3px;
            margin-top: 0;
        }

        .figure-leg {
            width: 16px;
            border-radius: 8px 8px 4px 4px;
        }

        /* 脚部 */
        .figure-feet {
            display: flex;
            gap: 3px;
            margin-top: 2px;
        }

        .figure-foot {
            width: 20px;
            height: 8px;
            border-radius: 10px 4px 4px 10px;
        }

        /* 男性样式 */
        .male {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            box-shadow: inset 0 2px 4px rgba(255,255,255,0.3), 0 2px 8px rgba(79,195,247,0.3);
        }

        .male-figure .figure-torso {
            width: 50px;
        }

        .male-figure .figure-waist {
            width: 38px;
        }

        .male-figure .figure-hips {
            width: 42px;
        }

        .male-figure .figure-leg {
            height: 100px;
        }

        /* 女性样式 */
        .female {
            background: linear-gradient(135deg, #f48fb1 0%, #ec407a 100%);
            box-shadow: inset 0 2px 4px rgba(255,255,255,0.3), 0 2px 8px rgba(244,143,177,0.3);
        }

        .female-figure .figure-torso {
            width: 42px;
        }

        .female-figure .figure-waist {
            width: 30px;
        }

        .female-figure .figure-hips {
            width: 45px;
        }

        .female-figure .figure-leg {
            height: 95px;
        }

        .figure-label {
            margin-top: 15px;
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
        }

        .figure-stats {
            margin-top: 8px;
            font-size: 12px;
            color: #6c757d;
            text-align: center;
            line-height: 1.4;
            background: rgba(255,255,255,0.8);
            padding: 8px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }

        /* 添加悬浮效果和动画 */
        .human-silhouette {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .human-silhouette:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.2));
        }

        /* 身高比例线 */
        .height-indicator {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            opacity: 0.6;
        }

        .male-height {
            bottom: 120px;
        }

        .female-height {
            bottom: 140px;
        }

        /* 添加身体部位的细节 */
        .figure-head::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 2px;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
            box-shadow:
                4px 0 0 rgba(255,255,255,0.8),
                -4px 0 0 rgba(255,255,255,0.8),
                0 8px 0 rgba(255,255,255,0.6);
        }

        /* 添加肌肉线条效果 */
        .figure-torso::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 1px;
            height: 60px;
            background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.3), transparent);
        }

        /* 添加关节点 */
        .figure-torso::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.5);
            border-radius: 50%;
            box-shadow:
                0 30px 0 rgba(255,255,255,0.5),
                0 60px 0 rgba(255,255,255,0.5);
        }

        /* 添加比例网格背景 */
        .human-figure::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(90deg, rgba(102,126,234,0.1) 1px, transparent 1px),
                linear-gradient(rgba(102,126,234,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            opacity: 0.3;
        }

        .height-chart {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            height: 300px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .height-bar {
            width: 40px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 5px 5px 0 0;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
        }

        .height-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
        }

        .height-value {
            position: absolute;
            top: -25px;
            font-size: 11px;
            color: #667eea;
            font-weight: 600;
        }

        .weight-distribution {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .weight-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .weight-category:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .weight-percentage {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .underweight { color: #17a2b8; }
        .normal { color: #28a745; }
        .overweight { color: #ffc107; }
        .obese { color: #dc3545; }

        .pie-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #4fc3f7 0deg 216deg,
                #f48fb1 216deg 360deg
            );
            margin: 20px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pie-center {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .weight-distribution {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>人口统计分析报告</h1>
            <p>基于2024年全国人口普查数据的综合分析</p>
        </div>

        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number">14.1亿</div>
                <div class="stat-label">总人口</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">51.2%</div>
                <div class="stat-label">男性比例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">48.8%</div>
                <div class="stat-label">女性比例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">169.7cm</div>
                <div class="stat-label">平均身高</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">65.2kg</div>
                <div class="stat-label">平均体重</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">22.6</div>
                <div class="stat-label">平均BMI</div>
            </div>
        </div>

        <div class="main-content">
            <div class="chart-section">
                <h3 class="chart-title">性别比例分布</h3>
                <div class="pie-chart">
                    <div class="pie-center">
                        <div style="font-size: 18px;">性别</div>
                        <div style="font-size: 14px; color: #6c757d;">比例</div>
                    </div>
                </div>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #4fc3f7;"></div>
                        <span>男性 51.2%</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f48fb1;"></div>
                        <span>女性 48.8%</span>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">人体画像对比</h3>
                <div class="human-figure">
                    <div class="figure male-figure">
                        <div class="human-silhouette">
                            <!-- 头部 -->
                            <div class="figure-head male"></div>
                            <!-- 颈部 -->
                            <div class="figure-neck male"></div>
                            <!-- 躯干 -->
                            <div class="figure-torso male">
                                <!-- 手臂 -->
                                <div class="figure-arms male"></div>
                                <div class="figure-arm-left male"></div>
                                <div class="figure-arm-right male"></div>
                            </div>
                            <!-- 腰部 -->
                            <div class="figure-waist male"></div>
                            <!-- 臀部 -->
                            <div class="figure-hips male"></div>
                            <!-- 腿部 -->
                            <div class="figure-legs">
                                <div class="figure-leg male"></div>
                                <div class="figure-leg male"></div>
                            </div>
                            <!-- 脚部 -->
                            <div class="figure-feet">
                                <div class="figure-foot male"></div>
                                <div class="figure-foot male"></div>
                            </div>
                        </div>
                        <div class="figure-label">男性</div>
                        <div class="figure-stats">
                            平均身高: 173.2cm<br>
                            平均体重: 70.8kg<br>
                            BMI: 23.6
                        </div>
                    </div>
                    <div class="figure female-figure">
                        <div class="human-silhouette">
                            <!-- 头部 -->
                            <div class="figure-head female"></div>
                            <!-- 颈部 -->
                            <div class="figure-neck female"></div>
                            <!-- 躯干 -->
                            <div class="figure-torso female">
                                <!-- 手臂 -->
                                <div class="figure-arms female"></div>
                                <div class="figure-arm-left female"></div>
                                <div class="figure-arm-right female"></div>
                            </div>
                            <!-- 腰部 -->
                            <div class="figure-waist female"></div>
                            <!-- 臀部 -->
                            <div class="figure-hips female"></div>
                            <!-- 腿部 -->
                            <div class="figure-legs">
                                <div class="figure-leg female"></div>
                                <div class="figure-leg female"></div>
                            </div>
                            <!-- 脚部 -->
                            <div class="figure-feet">
                                <div class="figure-foot female"></div>
                                <div class="figure-foot female"></div>
                            </div>
                        </div>
                        <div class="figure-label">女性</div>
                        <div class="figure-stats">
                            平均身高: 160.8cm<br>
                            平均体重: 58.9kg<br>
                            BMI: 22.8
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">身高分布统计</h3>
                <div class="height-chart">
                    <div class="height-bar" style="height: 60px;">
                        <div class="height-value">8.2%</div>
                        <div class="height-label">150cm以下</div>
                    </div>
                    <div class="height-bar" style="height: 120px;">
                        <div class="height-value">18.5%</div>
                        <div class="height-label">150-160cm</div>
                    </div>
                    <div class="height-bar" style="height: 200px;">
                        <div class="height-value">35.8%</div>
                        <div class="height-label">160-170cm</div>
                    </div>
                    <div class="height-bar" style="height: 180px;">
                        <div class="height-value">28.3%</div>
                        <div class="height-label">170-180cm</div>
                    </div>
                    <div class="height-bar" style="height: 80px;">
                        <div class="height-value">9.2%</div>
                        <div class="height-label">180cm以上</div>
                    </div>
                </div>
            </div>

            <div class="chart-section">
                <h3 class="chart-title">体重分布分析</h3>
                <div class="weight-distribution">
                    <div class="weight-category">
                        <div class="weight-percentage underweight">12.3%</div>
                        <div>偏瘦</div>
                        <div style="font-size: 12px; color: #6c757d;">BMI < 18.5</div>
                    </div>
                    <div class="weight-category">
                        <div class="weight-percentage normal">58.7%</div>
                        <div>正常</div>
                        <div style="font-size: 12px; color: #6c757d;">18.5 ≤ BMI < 24</div>
                    </div>
                    <div class="weight-category">
                        <div class="weight-percentage overweight">23.1%</div>
                        <div>超重</div>
                        <div style="font-size: 12px; color: #6c757d;">24 ≤ BMI < 28</div>
                    </div>
                </div>
                <div class="weight-distribution">
                    <div class="weight-category">
                        <div class="weight-percentage obese">5.9%</div>
                        <div>肥胖</div>
                        <div style="font-size: 12px; color: #6c757d;">BMI ≥ 28</div>
                    </div>
                    <div class="weight-category" style="grid-column: span 2;">
                        <div style="font-size: 16px; font-weight: 600; color: #28a745;">
                            健康体重人群占比: 58.7%
                        </div>
                        <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                            符合WHO健康标准
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-section full-width">
                <h3 class="chart-title">详细统计数据表</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>统计项目</th>
                            <th>男性</th>
                            <th>女性</th>
                            <th>总体</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>人口数量</td>
                            <td>7.22亿</td>
                            <td>6.88亿</td>
                            <td>14.1亿</td>
                            <td>2024年统计</td>
                        </tr>
                        <tr>
                            <td>平均身高</td>
                            <td>173.2cm</td>
                            <td>160.8cm</td>
                            <td>169.7cm</td>
                            <td>成年人群</td>
                        </tr>
                        <tr>
                            <td>平均体重</td>
                            <td>70.8kg</td>
                            <td>58.9kg</td>
                            <td>65.2kg</td>
                            <td>成年人群</td>
                        </tr>
                        <tr>
                            <td>平均BMI</td>
                            <td>23.6</td>
                            <td>22.8</td>
                            <td>22.6</td>
                            <td>正常范围</td>
                        </tr>
                        <tr>
                            <td>肥胖率</td>
                            <td>7.2%</td>
                            <td>4.6%</td>
                            <td>5.9%</td>
                            <td>BMI≥28</td>
                        </tr>
                        <tr>
                            <td>超重率</td>
                            <td>28.5%</td>
                            <td>17.7%</td>
                            <td>23.1%</td>
                            <td>24≤BMI<28</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>