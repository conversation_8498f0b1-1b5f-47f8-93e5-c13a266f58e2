<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 UI 设计方案 - 晶白风格</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #F8F4F0;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            color: #4A4A4A;
            margin-bottom: 30px;
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 30px;
            justify-items: center;
        }

        .screen {
            width: 375px;
            height: 812px;
            background: #FFFFFF;
            border: 1px solid #E0E0E0;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .screen-title {
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #888888;
        }

        /* Crystal White Style Components */
        .crystal-card {
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.1));
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.05));
            transition: all 0.3s ease;
        }

        .crystal-card:hover {
            transform: translateY(-4px);
            filter: drop-shadow(0 12px 20px rgba(0, 0, 0, 0.08));
        }

        .crystal-button {
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.2));
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            color: #4A4A4A;
            font-weight: 500;
            transition: all 0.2s ease;
            backdrop-filter: blur(8px);
            padding: 12px 24px;
            cursor: pointer;
        }

        .crystal-button:active {
            transform: scale(0.98);
            opacity: 0.8;
        }

        .crystal-input {
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            padding: 16px;
            font-size: 16px;
            color: #4A4A4A;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.03));
            width: 100%;
        }

        .crystal-input:focus {
            outline: none;
            box-shadow: 0 0 8px 2px rgba(255, 127, 80, 0.3);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #888888;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
        }

        .nav-item.active {
            color: #FF7F50;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        /* Text Styles */
        .text-primary {
            color: #4A4A4A;
        }

        .text-secondary {
            color: #888888;
        }

        .text-accent {
            color: #FF7F50;
        }

        .title-large {
            font-size: 30px;
            font-weight: 700;
        }

        .title-medium {
            font-size: 20px;
            font-weight: 500;
        }

        .body-text {
            font-size: 16px;
            font-weight: 400;
        }

        .caption {
            font-size: 14px;
            font-weight: 400;
        }

        /* Status Bar */
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #4A4A4A;
        }

        /* Screen Content */
        .screen-content {
            padding: 20px;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }

        .screen-content.full-height {
            height: calc(100% - 44px);
        }

        /* Splash Screen Styles */
        .splash-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
        }

        .app-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.3));
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            filter: drop-shadow(0 8px 24px rgba(0, 0, 0, 0.1));
        }

        .app-name {
            font-size: 32px;
            font-weight: 700;
            color: #4A4A4A;
            margin-bottom: 10px;
        }

        .app-tagline {
            font-size: 16px;
            color: #888888;
            margin-bottom: 40px;
        }

        /* Home Screen Styles */
        .greeting {
            margin-bottom: 30px;
        }

        .greeting-text {
            font-size: 24px;
            font-weight: 600;
            color: #4A4A4A;
            margin-bottom: 5px;
        }

        .greeting-subtext {
            font-size: 16px;
            color: #888888;
        }

        .daily-intention {
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .intention-text {
            font-size: 18px;
            font-style: italic;
            color: #4A4A4A;
            line-height: 1.5;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-button {
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .action-icon {
            width: 32px;
            height: 32px;
            fill: #FF7F50;
        }

        .action-text {
            font-size: 14px;
            font-weight: 500;
            color: #4A4A4A;
        }

        .goals-summary {
            padding: 20px;
        }

        .summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #4A4A4A;
            margin-bottom: 15px;
        }

        .goal-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .goal-item:last-child {
            border-bottom: none;
        }

        .goal-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, rgba(255, 127, 80, 0.2), rgba(255, 127, 80, 0.05));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .goal-info {
            flex: 1;
        }

        .goal-title {
            font-size: 16px;
            font-weight: 500;
            color: #4A4A4A;
            margin-bottom: 2px;
        }

        .goal-status {
            font-size: 14px;
            color: #888888;
        }

        /* Goal Detail Styles */
        .goal-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .goal-image {
            width: 120px;
            height: 120px;
            border-radius: 20px;
            object-fit: cover;
            margin: 0 auto 20px;
            display: block;
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
        }

        .goal-description {
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-section {
            padding: 20px;
            margin-bottom: 20px;
        }

        .progress-title {
            font-size: 18px;
            font-weight: 600;
            color: #4A4A4A;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-container {
            height: 200px;
            margin-bottom: 20px;
            position: relative;
        }

        .chart-svg {
            width: 100%;
            height: 100%;
        }

        .chart-line {
            fill: none;
            stroke: #FF7F50;
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 2s ease-in-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .chart-dot {
            fill: #FF7F50;
            r: 4;
        }

        .chart-grid {
            stroke: rgba(0, 0, 0, 0.1);
            stroke-width: 1;
        }

        .chart-label {
            font-size: 12px;
            fill: #888888;
            text-anchor: middle;
        }

        .affirmations-section {
            padding: 20px;
            margin-bottom: 20px;
        }

        .affirmation-item {
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .affirmation-text {
            font-size: 16px;
            color: #4A4A4A;
            font-style: italic;
            line-height: 1.5;
        }

        /* Form Styles */
        .form-section {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #4A4A4A;
            margin-bottom: 8px;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn-primary {
            flex: 1;
            background: linear-gradient(135deg, #FF7F50, #FF6B35);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary:active {
            transform: scale(0.98);
        }

        .btn-secondary {
            flex: 1;
            background: rgba(255, 255, 255, 0.8);
            color: #4A4A4A;
            border: 1px solid rgba(255, 255, 255, 0.4);
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-secondary:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <!-- SVG Icon Definitions -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-home" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </symbol>
            <symbol id="icon-target" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
            </symbol>
            <symbol id="icon-quote" viewBox="0 0 24 24">
                <path d="M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"/>
            </symbol>
            <symbol id="icon-journal" viewBox="0 0 24 24">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </symbol>
            <symbol id="icon-add" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </symbol>
            <symbol id="icon-check" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </symbol>
            <symbol id="icon-arrow-right" viewBox="0 0 24 24">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </symbol>
            <symbol id="icon-image" viewBox="0 0 24 24">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </symbol>
            <symbol id="icon-heart" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </symbol>
            <symbol id="icon-star" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <h1 class="title">显化应用 UI 设计方案 - 晶白风格</h1>
        
        <div class="screens-grid">
            <!-- Screen 1: Splash Screen -->
            <div class="screen">
                <div class="screen-title">启动页</div>
                <div class="splash-content">
                    <div class="app-logo">
                        <svg class="nav-icon" style="width: 60px; height: 60px; fill: #FF7F50;">
                            <use xlink:href="#icon-star"></use>
                        </svg>
                    </div>
                    <div class="app-name">显化</div>
                    <div class="app-tagline">让梦想成为现实</div>
                    <div class="crystal-button">开始体验</div>
                </div>
            </div>

            <!-- Screen 2: Home/Dashboard -->
            <div class="screen">
                <div class="screen-title">主页</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div class="greeting">
                        <div class="greeting-text">早安，小明</div>
                        <div class="greeting-subtext">今天也要充满正能量哦</div>
                    </div>

                    <div class="crystal-card daily-intention">
                        <div class="intention-text">"我值得拥有所有美好的事物，宇宙正在为我安排最好的一切。"</div>
                    </div>

                    <div class="quick-actions">
                        <div class="crystal-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            <span class="action-text">新建目标</span>
                        </div>
                        <div class="crystal-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-quote"></use>
                            </svg>
                            <span class="action-text">肯定语</span>
                        </div>
                        <div class="crystal-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-journal"></use>
                            </svg>
                            <span class="action-text">感恩日记</span>
                        </div>
                        <div class="crystal-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-image"></use>
                            </svg>
                            <span class="action-text">愿景板</span>
                        </div>
                    </div>

                    <div class="crystal-card goals-summary">
                        <div class="summary-title">我的目标</div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 20px; height: 20px; fill: #FF7F50;">
                                    <use xlink:href="#icon-target"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">健康生活</div>
                                <div class="goal-status">进行中 · 80% 完成</div>
                            </div>
                        </div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 20px; height: 20px; fill: #FF7F50;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">事业发展</div>
                                <div class="goal-status">进行中 · 60% 完成</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 3: Goals List -->
            <div class="screen">
                <div class="screen-title">目标列表</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-primary">我的目标</h2>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            新建
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <img src="https://picsum.photos/id/1011/80/80" alt="目标图片" style="width: 80px; height: 80px; border-radius: 12px; object-fit: cover;">
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">健康生活</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">每天运动30分钟，保持健康饮食</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="flex: 1; height: 6px; background: rgba(255, 127, 80, 0.2); border-radius: 3px;">
                                            <div style="width: 80%; height: 100%; background: #FF7F50; border-radius: 3px;"></div>
                                        </div>
                                        <span class="caption text-accent">80%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <img src="https://picsum.photos/id/1062/80/80" alt="目标图片" style="width: 80px; height: 80px; border-radius: 12px; object-fit: cover;">
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">事业发展</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">提升专业技能，获得晋升机会</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="flex: 1; height: 6px; background: rgba(255, 127, 80, 0.2); border-radius: 3px;">
                                            <div style="width: 60%; height: 100%; background: #FF7F50; border-radius: 3px;"></div>
                                        </div>
                                        <span class="caption text-accent">60%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <img src="https://picsum.photos/id/219/80/80" alt="目标图片" style="width: 80px; height: 80px; border-radius: 12px; object-fit: cover;">
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">财富自由</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">建立多元化收入来源</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="flex: 1; height: 6px; background: rgba(255, 127, 80, 0.2); border-radius: 3px;">
                                            <div style="width: 30%; height: 100%; background: #FF7F50; border-radius: 3px;"></div>
                                        </div>
                                        <span class="caption text-accent">30%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 4: Goal Detail with Chart -->
            <div class="screen">
                <div class="screen-title">目标详情</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div class="goal-header">
                        <img src="https://picsum.photos/id/1011/120/120" alt="健康生活" class="goal-image">
                        <h2 class="title-large text-primary">健康生活</h2>
                        <p class="body-text text-secondary">每天运动30分钟，保持健康饮食</p>
                    </div>

                    <div class="crystal-card progress-section">
                        <div class="progress-title">信心指数变化</div>
                        <div class="chart-container">
                            <svg class="chart-svg" viewBox="0 0 300 150">
                                <!-- Grid lines -->
                                <defs>
                                    <pattern id="grid" width="50" height="30" patternUnits="userSpaceOnUse">
                                        <path d="M 50 0 L 0 0 0 30" fill="none" class="chart-grid"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3"/>

                                <!-- Chart line -->
                                <path d="M 30 120 L 80 100 L 130 80 L 180 70 L 230 50 L 270 40" class="chart-line"/>

                                <!-- Data points -->
                                <circle cx="30" cy="120" class="chart-dot"/>
                                <circle cx="80" cy="100" class="chart-dot"/>
                                <circle cx="130" cy="80" class="chart-dot"/>
                                <circle cx="180" cy="70" class="chart-dot"/>
                                <circle cx="230" cy="50" class="chart-dot"/>
                                <circle cx="270" cy="40" class="chart-dot"/>

                                <!-- Labels -->
                                <text x="30" y="140" class="chart-label">1月</text>
                                <text x="80" y="140" class="chart-label">2月</text>
                                <text x="130" y="140" class="chart-label">3月</text>
                                <text x="180" y="140" class="chart-label">4月</text>
                                <text x="230" y="140" class="chart-label">5月</text>
                                <text x="270" y="140" class="chart-label">6月</text>
                            </svg>
                        </div>
                        <div style="text-align: center;">
                            <span class="caption text-secondary">信心指数持续上升，目前达到 85%</span>
                        </div>
                    </div>

                    <div class="crystal-card affirmations-section">
                        <div class="progress-title">相关肯定语</div>
                        <div class="affirmation-item">
                            <div class="affirmation-text">"我的身体每天都变得更加健康强壮"</div>
                        </div>
                        <div class="affirmation-item">
                            <div class="affirmation-text">"我享受运动带来的快乐和活力"</div>
                        </div>
                        <div class="affirmation-item">
                            <div class="affirmation-text">"健康的生活方式是我的自然选择"</div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 5: Create/Edit Goal -->
            <div class="screen">
                <div class="screen-title">创建目标</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content full-height">
                    <div class="form-section">
                        <h2 class="title-large text-primary" style="margin-bottom: 30px; text-align: center;">创建新目标</h2>

                        <div class="form-group">
                            <label class="form-label">目标名称</label>
                            <input type="text" class="crystal-input" placeholder="输入你的目标名称" value="学习新技能">
                        </div>

                        <div class="form-group">
                            <label class="form-label">目标描述</label>
                            <textarea class="crystal-input form-textarea" placeholder="详细描述你的目标...">每天学习编程2小时，在6个月内掌握新的编程语言，提升职业技能。</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">目标分类</label>
                            <select class="crystal-input">
                                <option>事业发展</option>
                                <option>健康生活</option>
                                <option>人际关系</option>
                                <option>财富管理</option>
                                <option>个人成长</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">目标图片</label>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <img src="https://picsum.photos/id/1062/60/60" alt="预览" style="width: 60px; height: 60px; border-radius: 8px; object-fit: cover;">
                                <button class="crystal-button" style="padding: 8px 16px; font-size: 14px;">选择图片</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">截止日期</label>
                            <input type="date" class="crystal-input" value="2024-12-31">
                        </div>

                        <div class="form-actions">
                            <button class="btn-secondary">取消</button>
                            <button class="btn-primary">保存目标</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Screen 6: Affirmations List -->
            <div class="screen">
                <div class="screen-title">肯定语列表</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-primary">肯定语</h2>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            添加
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(135deg, #FF7F50, #FF6B35); color: white;">全部</div>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">健康</div>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">事业</div>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">财富</div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div class="crystal-card" style="padding: 20px;">
                            <div class="affirmation-text" style="margin-bottom: 10px;">"我值得拥有所有美好的事物，宇宙正在为我安排最好的一切。"</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">通用 · 收藏</span>
                                <svg style="width: 20px; height: 20px; fill: #FF7F50;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div class="affirmation-text" style="margin-bottom: 10px;">"我的身体每天都变得更加健康强壮，我享受运动带来的活力。"</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">健康 · 收藏</span>
                                <svg style="width: 20px; height: 20px; fill: #FF7F50;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div class="affirmation-text" style="margin-bottom: 10px;">"我的事业蒸蒸日上，机会不断涌现，成功自然而然地到来。"</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">事业</span>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div class="affirmation-text" style="margin-bottom: 10px;">"金钱是能量的流动，我吸引着丰盛的财富进入我的生活。"</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">财富</span>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div class="affirmation-text" style="margin-bottom: 10px;">"我被爱包围着，我的人际关系和谐美好，充满理解与支持。"</div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">人际关系</span>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 7: Affirmation Detail -->
            <div class="screen">
                <div class="screen-title">肯定语练习</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content full-height">
                    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; text-align: center; padding: 40px 20px;">
                        <div class="crystal-card" style="padding: 40px; margin-bottom: 40px; width: 100%;">
                            <div style="font-size: 24px; font-weight: 600; color: #4A4A4A; line-height: 1.4; margin-bottom: 20px;">
                                "我值得拥有所有美好的事物，宇宙正在为我安排最好的一切。"
                            </div>
                            <div class="caption text-secondary">重复这句话，感受其中的力量</div>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 20px; width: 100%;">
                            <div class="crystal-button" style="padding: 16px; font-size: 16px; display: flex; align-items: center; justify-content: center; gap: 10px;">
                                <svg style="width: 20px; height: 20px; fill: currentColor;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                                收藏这句肯定语
                            </div>

                            <div class="crystal-button" style="padding: 16px; font-size: 16px;">开始5分钟练习</div>

                            <div style="display: flex; gap: 15px;">
                                <div class="crystal-button" style="flex: 1; padding: 12px; font-size: 14px;">上一句</div>
                                <div class="crystal-button" style="flex: 1; padding: 12px; font-size: 14px;">下一句</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Screen 8: Gratitude Journal List -->
            <div class="screen">
                <div class="screen-title">感恩日记</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-primary">感恩日记</h2>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            写日记
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h3 class="title-medium text-primary">今天</h3>
                                <span class="caption text-secondary">2024年6月18日</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">1.</span>
                                    <span class="body-text text-primary">感恩今天的阳光明媚，让我心情愉悦</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">2.</span>
                                    <span class="body-text text-primary">感恩家人的关爱和支持</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">3.</span>
                                    <span class="body-text text-primary">感恩工作中遇到的新机会</span>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h3 class="title-medium text-primary">昨天</h3>
                                <span class="caption text-secondary">2024年6月17日</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">1.</span>
                                    <span class="body-text text-primary">感恩朋友的陪伴和欢声笑语</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">2.</span>
                                    <span class="body-text text-primary">感恩美味的晚餐和温馨的家</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">3.</span>
                                    <span class="body-text text-primary">感恩学到的新知识</span>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h3 class="title-medium text-primary">6月16日</h3>
                                <span class="caption text-secondary">2024年6月16日</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">1.</span>
                                    <span class="body-text text-primary">感恩健康的身体</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">2.</span>
                                    <span class="body-text text-primary">感恩完成了重要的项目</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 10px;">
                                    <span style="color: #FF7F50; font-weight: 600;">3.</span>
                                    <span class="body-text text-primary">感恩遇到的善意陌生人</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 9: Add Journal Entry -->
            <div class="screen">
                <div class="screen-title">写感恩日记</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content full-height">
                    <div class="form-section">
                        <h2 class="title-large text-primary" style="margin-bottom: 10px; text-align: center;">今日感恩</h2>
                        <p class="body-text text-secondary" style="text-align: center; margin-bottom: 30px;">记录三件让你感恩的事情</p>

                        <div class="form-group">
                            <label class="form-label">感恩事项 1</label>
                            <textarea class="crystal-input" placeholder="今天让你感恩的第一件事..." style="min-height: 80px;">感恩今天的阳光明媚，让我心情愉悦</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">感恩事项 2</label>
                            <textarea class="crystal-input" placeholder="今天让你感恩的第二件事..." style="min-height: 80px;">感恩家人的关爱和支持</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">感恩事项 3</label>
                            <textarea class="crystal-input" placeholder="今天让你感恩的第三件事..." style="min-height: 80px;">感恩工作中遇到的新机会</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">今日心情</label>
                            <div style="display: flex; gap: 10px; justify-content: center;">
                                <div style="font-size: 32px; cursor: pointer; opacity: 0.3;">😢</div>
                                <div style="font-size: 32px; cursor: pointer; opacity: 0.3;">😐</div>
                                <div style="font-size: 32px; cursor: pointer;">😊</div>
                                <div style="font-size: 32px; cursor: pointer; opacity: 0.3;">😄</div>
                                <div style="font-size: 32px; cursor: pointer; opacity: 0.3;">🥰</div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button class="btn-secondary">取消</button>
                            <button class="btn-primary">保存日记</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Screen 10: Vision Board -->
            <div class="screen">
                <div class="screen-title">愿景板</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-primary">我的愿景板</h2>
                        <div class="crystal-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            添加
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div class="crystal-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                            <img src="https://picsum.photos/id/1040/200/200" alt="山景" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="crystal-card" style="padding: 15px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #4A4A4A; margin-bottom: 5px;">健康生活</div>
                            <div style="font-size: 14px; color: #888888;">每天运动30分钟</div>
                        </div>
                        <div class="crystal-card" style="padding: 15px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #4A4A4A; margin-bottom: 5px;">事业成功</div>
                            <div style="font-size: 14px; color: #888888;">获得理想职位</div>
                        </div>
                        <div class="crystal-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                            <img src="https://picsum.photos/id/219/200/200" alt="咖啡" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="crystal-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                            <img src="https://picsum.photos/id/1015/200/200" alt="湖景" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="crystal-card" style="padding: 15px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #4A4A4A; margin-bottom: 5px;">财富自由</div>
                            <div style="font-size: 14px; color: #888888;">实现财务目标</div>
                        </div>
                    </div>

                    <div class="crystal-card" style="padding: 20px; text-align: center;">
                        <div style="font-size: 20px; font-weight: 600; color: #4A4A4A; margin-bottom: 10px;">我的愿景宣言</div>
                        <div style="font-size: 16px; color: #4A4A4A; line-height: 1.5; font-style: italic;">
                            "我正在创造一个充满健康、成功和丰盛的生活。每一天，我都在朝着我的梦想更近一步。"
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 11: Settings -->
            <div class="screen">
                <div class="screen-title">设置</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="screen-content">
                    <h2 class="title-large text-primary" style="margin-bottom: 30px; text-align: center;">设置</h2>

                    <div class="crystal-card" style="padding: 20px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <img src="https://picsum.photos/id/237/60/60" alt="头像" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;">
                            <div style="flex: 1;">
                                <div class="title-medium text-primary" style="margin-bottom: 5px;">小明</div>
                                <div class="body-text text-secondary"><EMAIL></div>
                            </div>
                            <svg style="width: 20px; height: 20px; fill: #888888;">
                                <use xlink:href="#icon-arrow-right"></use>
                            </svg>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 2px;">每日提醒</div>
                                    <div class="caption text-secondary">每天9:00提醒写感恩日记</div>
                                </div>
                                <div style="width: 50px; height: 30px; background: #FF7F50; border-radius: 15px; position: relative; cursor: pointer;">
                                    <div style="width: 26px; height: 26px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.2s ease;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 2px;">肯定语提醒</div>
                                    <div class="caption text-secondary">每天3次随机肯定语推送</div>
                                </div>
                                <div style="width: 50px; height: 30px; background: rgba(0, 0, 0, 0.1); border-radius: 15px; position: relative; cursor: pointer;">
                                    <div style="width: 26px; height: 26px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.2s ease;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">主题设置</div>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-arrow-right"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">数据备份</div>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-arrow-right"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">帮助与反馈</div>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-arrow-right"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="crystal-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">关于应用</div>
                                <svg style="width: 20px; height: 20px; fill: #888888;">
                                    <use xlink:href="#icon-arrow-right"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
