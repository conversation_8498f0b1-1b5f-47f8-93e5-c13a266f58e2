<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 - 科技感Neumorphism设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            padding: 20px;
            color: #ffffff;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-family: 'Orbitron', monospace;
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(45deg, #00d4ff, #ff00ff, #00ff88);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            justify-items: center;
        }

        .screen {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1e1e3f, #0f0f23);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            box-shadow: 
                20px 20px 40px rgba(0, 0, 0, 0.5),
                -20px -20px 40px rgba(255, 255, 255, 0.05),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
        }

        .screen-title {
            position: absolute;
            top: -50px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        /* Neumorphism Components */
        .neuro-card {
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            border-radius: 20px;
            box-shadow: 
                8px 8px 16px rgba(0, 0, 0, 0.6),
                -8px -8px 16px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .neuro-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
        }

        .neuro-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                12px 12px 24px rgba(0, 0, 0, 0.7),
                -12px -12px 24px rgba(255, 255, 255, 0.08),
                0 0 30px rgba(0, 212, 255, 0.3);
        }

        .neuro-button {
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            color: #00d4ff;
            font-weight: 500;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 
                4px 4px 8px rgba(0, 0, 0, 0.5),
                -4px -4px 8px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }

        .neuro-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .neuro-button:hover::before {
            left: 100%;
        }

        .neuro-button:active {
            transform: scale(0.95);
            box-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.7),
                -2px -2px 4px rgba(255, 255, 255, 0.03);
        }

        .neuro-input {
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            color: #ffffff;
            padding: 16px;
            font-size: 16px;
            width: 100%;
            box-shadow: 
                inset 4px 4px 8px rgba(0, 0, 0, 0.5),
                inset -4px -4px 8px rgba(255, 255, 255, 0.05);
        }

        .neuro-input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 
                inset 4px 4px 8px rgba(0, 0, 0, 0.5),
                inset -4px -4px 8px rgba(255, 255, 255, 0.05),
                0 0 20px rgba(0, 212, 255, 0.3);
        }

        /* Glass Morphism */
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Status Bar */
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #00d4ff;
            background: rgba(0, 0, 0, 0.2);
        }

        /* Screen Content */
        .screen-content {
            padding: 20px;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }

        .screen-content.full-height {
            height: calc(100% - 44px);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            border-top: 1px solid rgba(0, 212, 255, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .nav-item:hover {
            color: #00d4ff;
            transform: translateY(-2px);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
            filter: drop-shadow(0 0 5px currentColor);
        }

        /* Text Styles */
        .text-primary {
            color: #ffffff;
        }

        .text-secondary {
            color: rgba(255, 255, 255, 0.7);
        }

        .text-accent {
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .text-neon {
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-large {
            font-size: 28px;
            font-weight: 700;
            font-family: 'Orbitron', monospace;
        }

        .title-medium {
            font-size: 20px;
            font-weight: 600;
        }

        .body-text {
            font-size: 16px;
            font-weight: 400;
        }

        .caption {
            font-size: 14px;
            font-weight: 400;
        }

        /* Particle Animation */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 6s infinite linear;
            box-shadow: 0 0 6px #00d4ff;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }

        /* Splash Screen Styles */
        .splash-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
            position: relative;
        }

        .app-logo {
            width: 150px;
            height: 150px;
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            border-radius: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            box-shadow: 
                15px 15px 30px rgba(0, 0, 0, 0.6),
                -15px -15px 30px rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(0, 212, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .app-logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.3), transparent);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            fill: #00d4ff;
            filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
            z-index: 1;
            position: relative;
        }

        .app-name {
            font-family: 'Orbitron', monospace;
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }

        .app-tagline {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 50px;
            font-weight: 300;
        }

        /* Dashboard Styles */
        .greeting {
            margin-bottom: 30px;
            text-align: center;
        }

        .greeting-text {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .greeting-subtext {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
        }

        .daily-intention {
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .daily-intention::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .intention-text {
            font-size: 18px;
            font-style: italic;
            color: #ffffff;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-button {
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-3px);
        }

        .action-icon {
            width: 32px;
            height: 32px;
            fill: #00d4ff;
            filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
        }

        .action-text {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
        }

        .goals-summary {
            padding: 25px;
        }

        .summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
            text-align: center;
        }

        .goal-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
        }

        .goal-item:last-child {
            border-bottom: none;
        }

        .goal-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                4px 4px 8px rgba(0, 0, 0, 0.5),
                -4px -4px 8px rgba(255, 255, 255, 0.05);
        }

        .goal-info {
            flex: 1;
        }

        .goal-title {
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .goal-status {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #ff00ff);
            border-radius: 3px;
            transition: width 1s ease;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        /* Chart Styles */
        .chart-container {
            height: 200px;
            margin: 20px 0;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
        }

        .chart-svg {
            width: 100%;
            height: 100%;
        }

        .chart-line {
            fill: none;
            stroke: url(#chartGradient);
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-in-out forwards;
            filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .chart-dot {
            fill: #00d4ff;
            r: 5;
            filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8));
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { r: 5; }
            50% { r: 7; }
        }

        .chart-grid {
            stroke: rgba(0, 212, 255, 0.1);
            stroke-width: 1;
        }

        .chart-label {
            font-size: 12px;
            fill: rgba(255, 255, 255, 0.7);
            text-anchor: middle;
        }

        /* Form Styles */
        .form-section {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn-primary {
            flex: 1;
            background: linear-gradient(145deg, #00d4ff, #0099cc);
            color: #ffffff;
            border: none;
            padding: 16px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow:
                4px 4px 8px rgba(0, 0, 0, 0.5),
                -4px -4px 8px rgba(255, 255, 255, 0.05);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow:
                6px 6px 12px rgba(0, 0, 0, 0.6),
                -6px -6px 12px rgba(255, 255, 255, 0.08);
        }

        .btn-primary:active {
            transform: scale(0.95);
        }

        .btn-secondary {
            flex: 1;
            background: linear-gradient(145deg, #1a1a2e, #0f0f23);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 16px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow:
                4px 4px 8px rgba(0, 0, 0, 0.5),
                -4px -4px 8px rgba(255, 255, 255, 0.05);
        }

        .btn-secondary:hover {
            color: #00d4ff;
            border-color: #00d4ff;
        }
    </style>
</head>
<body>
    <!-- SVG Icon Definitions -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-home" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </symbol>
            <symbol id="icon-target" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
            </symbol>
            <symbol id="icon-quote" viewBox="0 0 24 24">
                <path d="M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"/>
            </symbol>
            <symbol id="icon-journal" viewBox="0 0 24 24">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </symbol>
            <symbol id="icon-add" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </symbol>
            <symbol id="icon-star" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </symbol>
            <symbol id="icon-heart" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </symbol>
            <symbol id="icon-image" viewBox="0 0 24 24">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <h1 class="title">显化应用 - 科技感Neumorphism设计</h1>
        
        <div class="screens-grid">
            <!-- Screen 1: Splash Screen -->
            <div class="screen">
                <div class="screen-title">启动页</div>
                <div class="particles">
                    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                    <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
                    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
                    <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
                    <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
                    <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
                    <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
                    <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
                    <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
                </div>
                <div class="splash-content">
                    <div class="app-logo">
                        <svg class="logo-icon">
                            <use xlink:href="#icon-star"></use>
                        </svg>
                    </div>
                    <div class="app-name">显化</div>
                    <div class="app-tagline">科技赋能 · 梦想成真</div>
                    <div class="neuro-button">启动应用</div>
                </div>
            </div>

            <!-- Screen 2: Dashboard -->
            <div class="screen">
                <div class="screen-title">主页仪表盘</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div class="greeting">
                        <div class="greeting-text">早安，未来创造者</div>
                        <div class="greeting-subtext">今天也要充满科技感地显化梦想</div>
                    </div>

                    <div class="neuro-card daily-intention">
                        <div class="intention-text">"我是数字时代的显化大师，科技与意识完美融合，创造无限可能。"</div>
                    </div>

                    <div class="quick-actions">
                        <div class="neuro-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            <span class="action-text">新建目标</span>
                        </div>
                        <div class="neuro-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-quote"></use>
                            </svg>
                            <span class="action-text">肯定语</span>
                        </div>
                        <div class="neuro-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-journal"></use>
                            </svg>
                            <span class="action-text">感恩日记</span>
                        </div>
                        <div class="neuro-card action-button">
                            <svg class="action-icon">
                                <use xlink:href="#icon-image"></use>
                            </svg>
                            <span class="action-text">愿景板</span>
                        </div>
                    </div>

                    <div class="neuro-card goals-summary">
                        <div class="summary-title">目标进度监控</div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 24px; height: 24px; fill: #00d4ff;">
                                    <use xlink:href="#icon-target"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">AI辅助健康生活</div>
                                <div class="goal-status">进行中 · 智能追踪</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="goal-item">
                            <div class="goal-icon">
                                <svg style="width: 24px; height: 24px; fill: #ff00ff;">
                                    <use xlink:href="#icon-star"></use>
                                </svg>
                            </div>
                            <div class="goal-info">
                                <div class="goal-title">科技事业突破</div>
                                <div class="goal-status">进行中 · 数据驱动</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 70%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 3: Goals List -->
            <div class="screen">
                <div class="screen-title">目标管理中心</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-neon">智能目标</h2>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            创建
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 12px;"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">AI辅助健康生活</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">智能监测运动数据，AI定制营养方案</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 85%;"></div>
                                        </div>
                                        <span class="caption text-accent">85%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 12px;"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">科技事业突破</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">掌握前沿技术，成为行业领导者</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 70%;"></div>
                                        </div>
                                        <span class="caption text-accent">70%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; gap: 15px;">
                                <image href="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=80&h=80&fit=crop" width="80" height="80" style="border-radius: 12px;"/>
                                <div style="flex: 1;">
                                    <h3 class="title-medium text-primary" style="margin-bottom: 8px;">数字财富增长</h3>
                                    <p class="body-text text-secondary" style="margin-bottom: 12px;">区块链投资，被动收入系统</p>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar" style="flex: 1;">
                                            <div class="progress-fill" style="width: 45%;"></div>
                                        </div>
                                        <span class="caption text-accent">45%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 4: Goal Detail with Chart -->
            <div class="screen">
                <div class="screen-title">目标详情分析</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <image href="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=120&h=120&fit=crop" width="120" height="120" style="border-radius: 20px; margin-bottom: 20px; filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));"/>
                        <h2 class="title-large text-neon">AI辅助健康生活</h2>
                        <p class="body-text text-secondary">智能监测运动数据，AI定制营养方案</p>
                    </div>

                    <div class="neuro-card" style="padding: 20px; margin-bottom: 20px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h3 class="title-medium text-accent">信心指数趋势</h3>
                        </div>
                        <div class="chart-container">
                            <svg class="chart-svg" viewBox="0 0 300 150">
                                <defs>
                                    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
                                        <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#00ff88;stop-opacity:1" />
                                    </linearGradient>
                                    <pattern id="grid" width="50" height="30" patternUnits="userSpaceOnUse">
                                        <path d="M 50 0 L 0 0 0 30" fill="none" class="chart-grid"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3"/>

                                <path d="M 30 120 L 80 100 L 130 80 L 180 65 L 230 45 L 270 30" class="chart-line"/>

                                <circle cx="30" cy="120" class="chart-dot"/>
                                <circle cx="80" cy="100" class="chart-dot"/>
                                <circle cx="130" cy="80" class="chart-dot"/>
                                <circle cx="180" cy="65" class="chart-dot"/>
                                <circle cx="230" cy="45" class="chart-dot"/>
                                <circle cx="270" cy="30" class="chart-dot"/>

                                <text x="30" y="140" class="chart-label">1月</text>
                                <text x="80" y="140" class="chart-label">2月</text>
                                <text x="130" y="140" class="chart-label">3月</text>
                                <text x="180" y="140" class="chart-label">4月</text>
                                <text x="230" y="140" class="chart-label">5月</text>
                                <text x="270" y="140" class="chart-label">6月</text>
                            </svg>
                        </div>
                        <div style="text-align: center;">
                            <span class="caption text-secondary">AI分析显示信心指数持续上升，当前达到 <span class="text-accent">92%</span></span>
                        </div>
                    </div>

                    <div class="neuro-card" style="padding: 20px;">
                        <div style="text-align: center; margin-bottom: 15px;">
                            <h3 class="title-medium text-accent">智能肯定语</h3>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div class="glass-card" style="padding: 15px;">
                                <div style="font-size: 16px; color: #ffffff; font-style: italic; line-height: 1.5;">
                                    "我的身体是高效的生物机器，每天都在优化升级"
                                </div>
                            </div>
                            <div class="glass-card" style="padding: 15px;">
                                <div style="font-size: 16px; color: #ffffff; font-style: italic; line-height: 1.5;">
                                    "AI助手帮助我精准达成健康目标"
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 5: Affirmations -->
            <div class="screen">
                <div class="screen-title">智能肯定语</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-neon">AI肯定语</h2>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            生成
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-bottom: 25px; flex-wrap: wrap;">
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(145deg, #00d4ff, #0099cc); color: white;">全部</div>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">科技</div>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">健康</div>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">财富</div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="neuro-card" style="padding: 25px; position: relative; overflow: hidden;">
                            <div class="daily-intention" style="padding: 0; margin: 0;">
                                <div style="font-size: 18px; color: #ffffff; line-height: 1.6; margin-bottom: 15px; font-style: italic;">
                                    "我是数字时代的显化大师，科技与意识完美融合，创造无限可能。"
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span class="caption text-secondary">AI生成 · 科技类</span>
                                    <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                        <use xlink:href="#icon-heart"></use>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 25px;">
                            <div style="font-size: 18px; color: #ffffff; line-height: 1.6; margin-bottom: 15px; font-style: italic;">
                                "我的身体是高效的生物机器，每天都在优化升级，达到最佳状态。"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">AI生成 · 健康类</span>
                                <svg style="width: 20px; height: 20px; fill: rgba(255, 255, 255, 0.5);">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 25px;">
                            <div style="font-size: 18px; color: #ffffff; line-height: 1.6; margin-bottom: 15px; font-style: italic;">
                                "区块链和AI技术为我带来源源不断的被动收入。"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">AI生成 · 财富类</span>
                                <svg style="width: 20px; height: 20px; fill: #ff00ff;">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 25px;">
                            <div style="font-size: 18px; color: #ffffff; line-height: 1.6; margin-bottom: 15px; font-style: italic;">
                                "我掌握前沿技术，引领行业变革，成为未来的创造者。"
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="caption text-secondary">AI生成 · 事业类</span>
                                <svg style="width: 20px; height: 20px; fill: rgba(255, 255, 255, 0.5);">
                                    <use xlink:href="#icon-heart"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>

            <!-- Screen 6: Gratitude Journal -->
            <div class="screen">
                <div class="screen-title">数字感恩日记</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-neon">感恩记录</h2>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            记录
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 20px;">
                        <div class="neuro-card" style="padding: 25px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">今天</h3>
                                <span class="caption text-accent">2024.06.18</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00d4ff; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩AI技术让我的工作效率提升了300%</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff00ff; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩智能健康监测设备守护我的身体</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00ff88; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩区块链投资带来的被动收入</span>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 25px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">昨天</h3>
                                <span class="caption text-accent">2024.06.17</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00d4ff; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩VR技术让我体验了未来世界</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff00ff; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩智能家居系统的贴心服务</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00ff88; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩在线学习平台拓展了我的知识边界</span>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 25px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 class="title-medium text-primary">6月16日</h3>
                                <span class="caption text-accent">2024.06.16</span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00d4ff; font-weight: 600; font-size: 16px;">1.</span>
                                    <span class="body-text text-primary">感恩云计算让我随时随地工作</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #ff00ff; font-weight: 600; font-size: 16px;">2.</span>
                                    <span class="body-text text-primary">感恩5G网络的超高速连接</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <span style="color: #00ff88; font-weight: 600; font-size: 16px;">3.</span>
                                    <span class="body-text text-primary">感恩数字支付的便捷体验</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-journal"></use>
                        </svg>
                        <span>日记</span>
                    </div>
                </div>
            </div>

            <!-- Screen 7: Vision Board -->
            <div class="screen">
                <div class="screen-title">数字愿景板</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="title-large text-neon">未来愿景</h2>
                        <div class="neuro-button" style="padding: 8px 16px; font-size: 14px;">
                            <svg style="width: 16px; height: 16px; fill: currentColor; margin-right: 5px;">
                                <use xlink:href="#icon-add"></use>
                            </svg>
                            添加
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 25px;">
                        <div class="neuro-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">AI办公空间</div>
                            </div>
                        </div>
                        <div class="neuro-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #00d4ff; margin-bottom: 8px;">智能健康</div>
                            <div style="font-size: 14px; color: rgba(255, 255, 255, 0.8);">AI监测生命体征</div>
                        </div>
                        <div class="neuro-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #ff00ff; margin-bottom: 8px;">科技创业</div>
                            <div style="font-size: 14px; color: rgba(255, 255, 255, 0.8);">引领行业变革</div>
                        </div>
                        <div class="neuro-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">区块链财富</div>
                            </div>
                        </div>
                        <div class="neuro-card" style="padding: 0; overflow: hidden; aspect-ratio: 1; position: relative;">
                            <image href="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=200&h=200&fit=crop" width="100%" height="100%"/>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); padding: 15px; color: white;">
                                <div style="font-size: 14px; font-weight: 600;">太空探索</div>
                            </div>
                        </div>
                        <div class="neuro-card" style="padding: 20px; display: flex; flex-direction: column; justify-content: center; text-align: center; aspect-ratio: 1;">
                            <div style="font-size: 18px; font-weight: 600; color: #00ff88; margin-bottom: 8px;">数字生活</div>
                            <div style="font-size: 14px; color: rgba(255, 255, 255, 0.8);">智能家居生态</div>
                        </div>
                    </div>

                    <div class="neuro-card" style="padding: 25px; text-align: center;">
                        <div style="font-size: 20px; font-weight: 600; color: #ffffff; margin-bottom: 15px;">愿景宣言</div>
                        <div style="font-size: 16px; color: rgba(255, 255, 255, 0.9); line-height: 1.6; font-style: italic;">
                            "我是数字时代的先锋，用科技的力量创造无限可能。AI是我的助手，区块链是我的财富引擎，我正在构建一个智能、健康、富足的未来生活。"
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-image"></use>
                        </svg>
                        <span>愿景</span>
                    </div>
                </div>
            </div>

            <!-- Screen 8: Settings -->
            <div class="screen">
                <div class="screen-title">系统设置</div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div class="screen-content">
                    <h2 class="title-large text-neon" style="margin-bottom: 30px; text-align: center;">系统控制台</h2>

                    <div class="neuro-card" style="padding: 25px; margin-bottom: 25px;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <image href="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=70&h=70&fit=crop" width="70" height="70" style="border-radius: 50%; filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.3));"/>
                            <div style="flex: 1;">
                                <div class="title-medium text-primary" style="margin-bottom: 5px;">数字创造者</div>
                                <div class="body-text text-secondary"><EMAIL></div>
                            </div>
                            <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                <use xlink:href="#icon-settings"></use>
                            </svg>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 5px;">AI智能提醒</div>
                                    <div class="caption text-secondary">每天3次个性化推送</div>
                                </div>
                                <div style="width: 50px; height: 28px; background: linear-gradient(145deg, #00d4ff, #0099cc); border-radius: 14px; position: relative; cursor: pointer;">
                                    <div style="width: 24px; height: 24px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="body-text text-primary" style="margin-bottom: 5px;">生物识别解锁</div>
                                    <div class="caption text-secondary">面部识别 + 指纹验证</div>
                                </div>
                                <div style="width: 50px; height: 28px; background: rgba(255, 255, 255, 0.1); border-radius: 14px; position: relative; cursor: pointer;">
                                    <div style="width: 24px; height: 24px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">全息主题设置</div>
                                <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">区块链数据备份</div>
                                <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">AI助手训练</div>
                                <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>

                        <div class="neuro-card" style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="body-text text-primary">关于未来应用</div>
                                <svg style="width: 20px; height: 20px; fill: #00d4ff;">
                                    <use xlink:href="#icon-settings"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-home"></use>
                        </svg>
                        <span>主页</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-target"></use>
                        </svg>
                        <span>目标</span>
                    </div>
                    <div class="nav-item">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-quote"></use>
                        </svg>
                        <span>肯定语</span>
                    </div>
                    <div class="nav-item active">
                        <svg class="nav-icon">
                            <use xlink:href="#icon-settings"></use>
                        </svg>
                        <span>设置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate particles
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
            });

            // Add click effects to buttons
            const buttons = document.querySelectorAll('.neuro-button, .btn-primary, .btn-secondary');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(0, 212, 255, 0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - button.offsetLeft) + 'px';
                    ripple.style.top = (e.clientY - button.offsetTop) + 'px';
                    ripple.style.width = ripple.style.height = '20px';

                    button.style.position = 'relative';
                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.neuro-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
